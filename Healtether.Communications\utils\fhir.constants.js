//////////*************ARTIFACTS**************\\\\\\\\\\\\\\
//CLINICAL ARTIFACTS
export const DIAGNOSTIC_REPORT_RECORD = "DiagnosticReportRecord";
export const DIAGNOSTIC_REPORT_MEDIA_RECORD = "DiagnosticReportMediaRecord";
export const DISCHARGE_SUMMARY_RECORD = "DischargeSummaryRecord";
export const HEALTH_DOCUMENT_RECORD = "HealthDocumentRecord";
export const IMMUNIZATION_RECORD = "ImmunizationRecord";
export const OP_CONSULT_RECORD = "OPConsultRecord";
export const PRESCRIPTION_RECORD = "PrescriptionRecord";
export const WELLNESS_RECORD = "WellnessRecord";

//BILLING ARTIFACTS
export const INVOICE_RECORD = "InvoiceRecord";

///////************METADATA and SNOMED CONSTANTS**********\\\\\\\\

////////////*************METADATA*************\\\\\\\\\\\\\\\
export const bundleMetadata = (currentTime) => {
  return {
    versionId: "1",
    lastUpdated: currentTime,
    profile: [
      "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle",
    ],
    security: [
      {
        system: "http://terminology.hl7.org/CodeSystem/v3-Confidentiality",
        code: "V",
        display: "very restricted",
      },
    ],
  };
};

//OP CONSULT STARTS
export const bundleIdentifier = (systemUrl, systemId) => {
  return {
    system: systemUrl,
    value: systemId,
  };
};

export const compositionOPConsultMetadata = (currentTime) => {
  return {
    versionId: "1",
    lastUpdated: currentTime,
    profile: [
      "https://nrces.in/ndhm/fhir/r4/StructureDefinition/OPConsultRecord",
    ],
  };
};

export const compositionImmunizationMetadata = (currentTime) => {
  return {
    versionId: "1",
    lastUpdated: currentTime,
    profile: [
      "https://nrces.in/ndhm/fhir/r4/StructureDefinition/ImmunizationRecord",
    ],
  };
};

export const patientMetadata = (currentTime) => {
  return {
    versionId: "1",
    lastUpdated: currentTime,
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Patient"],
  };
};

export const chargeItemMetaData=()=>{
  return {
  versionId : "1",
  lastUpdated : "2023-08-21T11:30:00.181+05:30",
  profile : [
    "https://nrces.in/ndhm/fhir/r4/StructureDefinition/ChargeItem"
  ]
}
}
export const observationMetadata = () => {
  return {
    profile: [
      "https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns",
    ],
  };
};

export const invoiceMetaData=(currentTime)=>{
  return {
      versionId : "1",
      lastUpdated :currentTime,
      profile : [
         "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Invoice"
      ]
  }
}
export const diagnosticReportMetaData=()=>{
  return  {
   profile: [
    "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DiagnosticReportLab"
  ]
  }
}
export const immunizationMetaData = (currentTime) => {
  return {
    versionId: "1",
    lastUpdated: currentTime,
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Immunization"],
  };
};
export const observationCategory = () => {
  return {
    coding: [
      {
        system: "http://terminology.hl7.org/CodeSystem/observation-category",
        code: "vital-signs",
        display: "Vital Signs",
      },
    ],
    text: "Vital Signs",
  };
};
export const patientIdentifier = (id) => {
  return {
    type: {
      coding: [
        {
          system: "http://terminology.hl7.org/CodeSystem/v2-0203",
          code: "MR",
          display: "Medical record number",
        },
      ],
    },
    system: "https://abha.abdm.gov.in/abha/v3",
    value: id,
  };
};
export const patientAbhaNumberIdentifier=(number)=>{
  return {
    type: {
      coding: [
        {
          system: "http://terminology.hl7.org/CodeSystem/v2-0203",
          code: "MR",
          display: "Medical record number",
        },
      ],
      text:"ABHAID"
    },
    system: "https://abha.abdm.gov.in/abha/v3",
    value: number,
  };

}

export const patientAbhaAddressIdentifier=(address)=>{
  return {
    type: {
      coding: [
        {
          system: "http://terminology.hl7.org/CodeSystem/v2-0203",
          code: "MR",
          display: "Medical record number",
        },
      ],
      text:"ABHAADDRESS"
    },
    system: "https://abha.abdm.gov.in/abha/v3",
    value: address,
  };

}

export const encounterMetadata = (currentTime) => {
  return {
    lastUpdated: currentTime,
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Encounter"],
  };
};

export const encounterIdentifier = (hipId) => {
  return {
    system: "https://ndhm.in",
    value: hipId,
  };
};

export const encounterClass = () => {
  return {
    system: "http://terminology.hl7.org/CodeSystem/v3-ActCode",
    code: "AMB",
    display: "ambulatory",
  };
};

export const conditionMetadata = () => {
  return {
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Condition"],
  };
};

export const conditionClinicalStatus = (code, display) => {
  return {
    coding: [
      {
        system: "http://terminology.hl7.org/CodeSystem/condition-clinical",
        code: code,
        display: display,
      },
    ],
  };
};

export const practitionerMetadata = (currentTime) => {
  return {
    versionId: "1",
    lastUpdated: currentTime,
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Practitioner"],
  };
};

export const practitionerIdentifiers = (licenses) => {
  return licenses.map((license) => ({
    type: {
      coding: [
        {
          system: "http://terminology.hl7.org/CodeSystem/v2-0203",
          code: license.code,
          display: license.display,
        },
      ],
    },
    system: "https://doctor.ndhm.gov.in",
    value: license.licNo,
  }));
};

export const organizationMetadata = () => {
  return {
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Organization"],
  };
};

export const organizationIdentifiers = (licenses) => {
  return licenses.map((license) => ({
    type: {
      coding: [
        {
          system: "http://terminology.hl7.org/CodeSystem/v2-0203",
          code: license.code,
          display: license.display,
        },
      ],
    },
    system: "https://facility.ndhm.gov.in",
    value: license.licNo,
  }));
};

export const allergyIntoleranceMetadata = () => {
  return {
    profile: [
      "https://nrces.in/ndhm/fhir/r4/StructureDefinition/AllergyIntolerance",
    ],
  };
};

export const allergyIntoleranceClinicalStatus = (code, display) => {
  return {
    coding: [
      {
        system:
          "http://terminology.hl7.org/CodeSystem/allergyintolerance-clinical",
        code: code,
        display: display,
      },
    ],
  };
};

export const allergyIntoleranceVerificationStatus = (code, display) => {
  return {
    coding: [
      {
        system:
          "http://terminology.hl7.org/CodeSystem/allergyintolerance-verification",
        code: code,
        display: display,
      },
    ],
  };
};

export const serviceRequestMetadata = () => {
  return {
    profile: [
      "https://nrces.in/ndhm/fhir/r4/StructureDefinition/ServiceRequest",
    ],
  };
};

export const medicationStatementMetadata = () => {
  return {
    profile: [
      "https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationStatement",
    ],
  };
};

export const medicationRequestMetadata = () => {
  return {
    profile: [
      "https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationRequest",
    ],
  };
};

export const procedureMetadata = () => {
  return {
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Procedure"],
  };
};

export const documentReferenceMetadata = () => {
  return {
    profile: [
      "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentReference",
    ],
  };
};

export const appointmentMetadata = () => {
  return {
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Appointment"],
  };
};

//OP CONSULT ENDS

//PRESCRIPTION STARTS

export const compositionPrescriptionMetadata = (currentTime) => {
  return {
    versionId: "1",
    lastUpdated: currentTime,
    profile: [
      "https://nrces.in/ndhm/fhir/r4/StructureDefinition/PrescriptionRecord",
    ],
  };
};

export const binaryMetadata = () => {
  return {
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Binary"],
  };
};

//PRESCRIPTION ENDS

//WELLNESS STARTS
export const compositionWellnessMetadata = (currentTime) => {
  return {
    versionId: "1",
    lastUpdated: currentTime,
    profile: [
      "https://nrces.in/ndhm/fhir/r4/StructureDefinition/WellnessRecord",
    ],
  };
};

//DIAGNOSTIC REPORTS

export const compositionDiagnosticReportMetadata = (currentTime) => {
  return {
    versionId: "1",
    lastUpdated: currentTime,
    profile: [
      "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DiagnosticReportRecord",
    ],
  };
};

export const diagnosticReport = (currentTime) => {
  return {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: "721981007",
        display: "Diagnostic studies report",
      },
    ],
     text : "Diagnostic Report- Lab"
  }
};

// INVOICE
export const compositionInvoiceMetadata = (currentTime) => {
  return {
    versionId: "1",
    lastUpdated: currentTime,
    profile: [
      "https://nrces.in/ndhm/fhir/r4/StructureDefinition/InvoiceRecord",
    ],
  };
};

export const invoiceReport = () => {
  return {
          "text" : "Invoice Record"
        }
  //  {
  //   versionId: "1",
  //   lastUpdated: currentTime,
  //   profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Invoice"],
  // };
};
//HEALTH DOCUMENT STARTS
export const compositionHealthDocumentMetadata = (currentTime) => {
  return {
    versionId: "1",
    lastUpdated: currentTime,
    profile: [
      "https://nrces.in/ndhm/fhir/r4/StructureDefinition/HealthDocumentRecord",
    ],
  };
};

//////////***********SNOMED**************\\\\\\\\\\\\\\\
export const clinicalConsultationReport = () => {
  return {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: "371530004",
        display: "Clinical consultation report",
      },
    ],
    text: "Clinical consultation report",
  };
};

export const immunizationReport = () => {
  return {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: "41000179103",
        display: "Immunization record",
      },
    ],
    text: "Immunization record",
  };
};

export const prescriptionReport = () => {
  return {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: "440545006",
        display: "Prescription record",
      },
    ],
    text: "Prescription record",
  };
};

export const wellnessReport = () => {
  return {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: "**********",
        display: "Wellness Record",
      },
    ],
    text: "Wellness Record",
  };
};

export const healthDocumentReport = () => {
  return {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: "419891008",
        display: "Record artifact",
      },
    ],
    text: "Record artifact",
  };
};

export const chiefComplaints = (conditionResources) => {
  return {
    title: "Chief complaints",
    code: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "422843007",
          display: "Chief complaint section",
        },
      ],
      text: "Chief complaint section",
    },
    entry: conditionResources.map((condition) => ({
      reference: `urn:uuid:${condition.resource.id}`,
      display: condition.resource.resourceType,
    })),
  };
};

export const allergies = (allergyIntolerances) => {
  return {
    title: "Allergies",
    code: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "722446000",
          display: "Allergy record",
        },
      ],
    },
    entry: allergyIntolerances.map((allergy) => ({
      reference: `urn:uuid:${allergy.resource.id}`,
      display: allergy.resource.resourceType,
    })),
  };
};

export const medicalHistory = (conditionResources) => {
  return {
    title: "Medical History",
    code: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "371529009",
          display: "History and physical report",
        },
      ],
    },
    entry: conditionResources.map((condition) => ({
      reference: `urn:uuid:${condition.resource.id}`,
      display: condition.resource.resourceType,
    })),
  };
};

export const familyHistory = (familyMemberHistoryResources) => {
  return {
    title: "FamilyHistory",
    code: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "422432008",
          display: "Family history section",
        },
      ],
    },
    entry: familyMemberHistoryResources.map((familyHistory) => ({
      reference: `urn:uuid:${familyHistory.resource.id}`,
      display: familyHistory.resource.resourceType,
    })),
  };
};

export const observations = (observationResources) => {
  return {
    title: "Vital Signs",
    code: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "118227000",
          display: "Vital signs",
        },
      ],
    },
    entry: observationResources.map((observation) => ({
      reference: `urn:uuid:${observation.resource.id}`,
      display: observation.resource.resourceType,
    })),
  };
};

export const investigationAdvice = (serviceRequestResources) => {
  return {
    title: "Investigation Advice",
    code: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "721963009",
          display: "Order document",
        },
      ],
    },
    entry: serviceRequestResources.map((serviceRequest) => ({
      reference: `urn:uuid:${serviceRequest.resource.id}`,
      display: serviceRequest.resource.resourceType,
    })),
  };
};

export const medications = (
  medicationStatementResources,
  medicationRequestResources
) => {
  return {
    title: "Medications",
    code: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "721912009",
          display: "Medication summary document",
        },
      ],
    },
    entry: [
      ...medicationStatementResources.map((medication) => ({
        reference: `urn:uuid:${medication.resource.id}`,
        display: medication.resource.resourceType,
      })),
      ...medicationRequestResources.map((medication) => ({
        reference: `urn:uuid:${medication.resource.id}`,
        display: medication.resource.resourceType,
      })),
    ],
  };
};

export const procedure = (procedureResources) => {
  return {
    title: "Procedure",
    code: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "371525003",
          display: "Clinical procedure report",
        },
      ],
    },
    entry: procedureResources.map((procedureResource) => ({
      reference: `urn:uuid:${procedureResource.resource.id}`,
      display: procedureResource.resource.resourceType,
    })),
  };
};

export const appointment = (
  appointmentResourceId,
  appointmentResourceResourceType
) => {
  return {
    title: "Follow Up",
    code: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "736271009",
          display: "Outpatient care plan",
        },
      ],
    },
    entry: [
      {
        reference: `urn:uuid:${appointmentResourceId}`,
        display: appointmentResourceResourceType,
      },
    ],
  };
};

export const documentReference = (documentReferenceResources) => {
  return {
    title: "Document Reference",
    code: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "371530004",
          display: "Clinical consultation report",
        },
      ],
    },
    entry: documentReferenceResources.map((documentReferenceResource) => ({
      reference: `urn:uuid:${documentReferenceResource.resource.id}`,
      display: documentReferenceResource.resource.resourceType,
    })),
  };
};

export const prescriptionReportWithEntry = (
  binaryResourceId,
  binaryResourceResourceType,
  medicationRequestResources
) => {
  return {
    title: "Prescription record",
    code: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "440545006",
          display: "Prescription record",
        },
      ],
    },
    entry: [
      {
        reference: `urn:uuid:${binaryResourceId}`,
        type: binaryResourceResourceType,
      },
      ...medicationRequestResources.map((medication) => ({
        reference: `urn:uuid:${medication.resource.id}`,
        type: medication.resource.resourceType,
      })),
    ],
  };
};

export const healthDocumentReportWithEntry = (documentReferenceResources) => {
  return {
    title: "Record artifact",
    code: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "419891008",
          display: "Record artifact",
        },
      ],
    },
    entry: documentReferenceResources.map((documentReferenceResource) => ({
      reference: `urn:uuid:${documentReferenceResource.resource.id}`,
      display: documentReferenceResource.resource.resourceType,
    })),
  };
};

export const invoiceCodeConstant = () => {
  return {
    coding: [
      {
        system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-billing-codes",
        code: "00",
        display: "Consultation",
      },
    ],
  };
};

export const basePriceComponent = () => {
  return {
    coding: [
      {
        system:
          "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
        code: "01",
        display: "Rate",
      },
    ],
  };
};
export const informationPriceComponent = () => {
  return {
    coding: [
      {
        system:
          "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
        code: "00",
        display: "MRP",
      },
    ],
  };
};
export const discountPriceComponent = () => {
  return {
    coding: [
      {
        system:
          "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
        code: "02",
        display: "Discount",
      },
    ],
  };
};
export const taxCGSTPriceComponent = () => {
  return {
    coding: [
      {
        system:
          "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
        code: "03",
        display: "CGST",
      },
    ],
  };
};

export const taxSGSTPriceComponent = () => {
  return {
    coding: [
      {
        system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
        code: "04",
        display: "SGST"
      }
    ]
  }
}

export const taxIGSTPriceComponent = () => {
  return {
    coding: [
      {
        system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
        code: "05",
        display: "IGST"
      }
    ]
  }
}
// export const invoiceTypeConst = () => {
//   return {
//     coding: [
//       {
//         system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-billing-codes",
//         code: "01",
//         display: "Pharmacy",
//       },
//     ],
//   };
// };

export const getSnomedCtCode = (code, term,notes=undefined) => {
  return {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: code,
        display: term,
      },
    ],
    text: notes || term,
  };
};

export const signatureConstant = () => {
  return [
    {
      system: "urn:iso-astm:E1762-95:2013",
      code: "1.2.840.10065.1.12.1.1",
      display: "Author's Signature",
    },
  ];
};

///////////*******DIV CONSTANTS**********\\\\\\\\\\\\\

export const compositionDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Composition Record</p></div>",
  };
};

export const practitionerDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Practitioner Record</p></div>",
  };
};

export const organizationDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Organization Record</p></div>",
  };
};

export const chargeItemDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>chargeItemDiv Record</p></div>",
  };
};

export const patientDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Patient Record</p></div>",
  };
};

export const encounterDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang=en'><p>Encounter Record</p></div>",
  };
};

export const allergyDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>AllergyIntolerance Record</p></div>",
  };
};
export const observationDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Observation Record</p></div>",
  };
};
export const immunizationDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Immunization Record</p></div>",
  };
};
export const ImmunizationRecommendation = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>ImmunizationRecommendation Record</p></div>",
  };
};

export const appointmentDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Appointment Record</p></div>",
  };
};

export const conditionDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Condition Record</p></div>",
  };
};

export const procedureDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Procedure Record</p></div>",
  };
};
export const diagnosticReportDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>diagnostic report Record</p></div>",
  };
};

export const serviceRequestDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>ServiceRequest Record</p></div>",
  };
};

export const medicationStatementDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>MedicationStatement Record</p></div>",
  };
};

export const medicationRequestDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>MedicationRequest Record</p></div>",
  };
};

export const documentReferenceDiv = () => {
  return {
    status: "generated",
    div: "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>DocumentReference Record</p></div>",
  };
};
